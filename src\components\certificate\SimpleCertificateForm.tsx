'use client';

import React from 'react';
import { CertificateData, CertificateTemplate } from '@/types/certificate';

interface SimpleCertificateFormProps {
  template: CertificateTemplate;
  formData: CertificateData;
  onFormDataChange: (data: CertificateData) => void;
}

export default function SimpleCertificateForm({
  template,
  formData,
  onFormDataChange,
}: SimpleCertificateFormProps) {
  const handleInputChange = (field: keyof CertificateData, value: string) => {
    onFormDataChange({
      ...formData,
      [field]: value,
    });
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 space-y-6">
      {/* Name Field */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Name
        </label>
        <textarea
          id="name"
          placeholder="Name of the awardee"
          value={formData.recipientName}
          onChange={(e) => handleInputChange('recipientName', e.target.value)}
          maxLength={template.constraints.nameMaxLength}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={2}
        />
      </div>

      {/* Subtitle Field */}
      <div>
        <label htmlFor="subtitle" className="block text-sm font-medium text-gray-700 mb-2">
          Subtitle
        </label>
        <textarea
          id="subtitle"
          placeholder="Appears under the name of the awardee"
          value={formData.details}
          onChange={(e) => handleInputChange('details', e.target.value)}
          maxLength={template.constraints.detailsMaxLength}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={3}
        />
      </div>

      {/* Date Field */}
      <div>
        <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
          Date
        </label>
        <textarea
          id="date"
          placeholder=""
          value={formData.date}
          onChange={(e) => handleInputChange('date', e.target.value)}
          maxLength={template.constraints.dateMaxLength}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={2}
        />
      </div>

      {/* Signature Field */}
      <div>
        <label htmlFor="signature" className="block text-sm font-medium text-gray-700 mb-2">
          Signature
        </label>
        <textarea
          id="signature"
          placeholder="Name of signer"
          value={formData.signature}
          onChange={(e) => handleInputChange('signature', e.target.value)}
          maxLength={template.constraints.signatureMaxLength}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={2}
        />
      </div>
    </div>
  );
}
