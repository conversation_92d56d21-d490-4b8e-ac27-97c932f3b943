'use client';

import React, { useState, useCallback } from 'react';

import { Button } from '@/components/ui/button';

import SimpleCertificateForm from './SimpleCertificateForm';
import CertificatePreview from './CertificatePreview';
import { CertificateData, CertificateTemplate } from '@/types/certificate';
import { getDefaultTemplate } from '@/lib/certificate-templates';
import { Download, Loader2, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { certificateAnalytics } from '@/lib/analytics';
import { generateCertificatePDF } from '@/lib/pdf-generator';
import { Progress } from '@/components/ui/progress';
import { FadeIn, SlideIn, ScaleIn } from '@/components/ui/animations';

interface CertificateMakerProps {
  selectedTemplate?: CertificateTemplate;
  templates?: CertificateTemplate[];
  onTemplateChange?: (template: CertificateTemplate) => void;
}

export default function CertificateMaker({
  selectedTemplate: initialTemplate,
  templates = [],
  onTemplateChange
}: CertificateMakerProps) {
  const { toast } = useToast();
  // 直接使用传入的模板，不再支持模板切换
  const selectedTemplate = initialTemplate || getDefaultTemplate();

  const [formData, setFormData] = useState<CertificateData>({
    templateId: getDefaultTemplate().id,
    recipientName: '',
    date: '',
    signature: '',
    details: '',
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);



  const handleFormDataChange = useCallback((data: Partial<CertificateData>) => {
    setFormData(prev => ({
      ...prev,
      ...data,
    }));
  }, []);

  const handleGeneratePDF = useCallback(async () => {
    if (!selectedTemplate || !formData.recipientName || !formData.date || !formData.signature || !formData.details) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields before generating the certificate.",
        variant: "destructive",
      });

      // 跟踪错误
      certificateAnalytics.errorOccurred('validation_error', 'Missing required fields', 'pdf_generation');
      return;
    }

    // 跟踪生成开始
    certificateAnalytics.certificateGenerationStarted(selectedTemplate.id, formData);

    const startTime = Date.now();
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      // 生成并下载PDF
      await generateCertificatePDF(selectedTemplate, formData);

      clearInterval(progressInterval);
      setGenerationProgress(100);

      const endTime = Date.now();
      const generationTime = endTime - startTime;

      // 跟踪生成完成
      certificateAnalytics.certificateGenerated(selectedTemplate.id, generationTime);

      toast({
        title: "Certificate Generated!",
        description: "Your certificate has been generated and downloaded successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error('Error generating PDF:', error);

      // 跟踪错误
      certificateAnalytics.errorOccurred('generation_error', error instanceof Error ? error.message : 'Unknown error', 'pdf_generation');

      toast({
        title: "Generation Failed",
        description: "There was an error generating your certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  }, [selectedTemplate, formData, toast]);

  const isFormValid = formData.recipientName && formData.date && formData.signature && formData.details;

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* 单列布局 - 参考 Bannerbear 设计 */}
      <div className="space-y-8">


        {/* 表单和预览 - 统一的左右布局 */}
        <div className="space-y-8">
          {/* 竖向模板的模板选择器 */}
          {selectedTemplate.orientation === 'portrait' && templates.length > 1 && (
            <SlideIn direction="up" delay={100}>
              <div className="text-center">
                {/* <h3 className="text-2xl font-bold text-gray-900 mb-6">Choose Your Template</h3> */}
                <div className="flex justify-center">
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 max-w-6xl">
                    {templates.map((template) => (
                      <div
                        key={template.id}
                        className={`relative cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                          selectedTemplate?.id === template.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        } w-full max-w-xs`}
                        onClick={() => onTemplateChange?.(template)}
                      >
                        <div className="p-3">
                          <div className="bg-gray-50 rounded-lg overflow-hidden aspect-[3/4] mb-3">
                            <img
                              src={template.preview}
                              alt={template.displayName}
                              className="w-full h-full object-contain p-1"
                              draggable={false}
                            />
                          </div>
                          <div className="text-center">
                            <h4 className="font-medium text-sm text-gray-900 mb-1">
                              {template.displayName}
                            </h4>
                            <p className="text-gray-600 text-xs line-clamp-2">
                              {template.description}
                            </p>
                          </div>
                          {selectedTemplate?.id === template.id && (
                            <div className="absolute top-2 right-2">
                              <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                                <CheckCircle className="w-3 h-3 text-white" />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </SlideIn>
          )}

          {/* 表单和预览的左右布局 - 横向和竖向模板使用相同布局 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 左侧：表单 */}
            <SlideIn direction="left" delay={200}>
              <div className="space-y-6">
                <SimpleCertificateForm
                  template={selectedTemplate}
                  formData={formData}
                  onFormDataChange={handleFormDataChange}
                />

                {/* 生成按钮 */}
                <div className="space-y-4">
                  <Button
                    onClick={handleGeneratePDF}
                    disabled={!isFormValid || isGenerating}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-medium transition-all duration-200 hover:shadow-lg"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Generating PDF... {generationProgress}%
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-5 w-5" />
                        Create PDF
                      </>
                    )}
                  </Button>

                  {isGenerating && (
                    <ScaleIn>
                      <div className="space-y-2">
                        <Progress value={generationProgress} className="w-full" />
                        <p className="text-sm text-center text-gray-600">
                          Generating your certificate...
                        </p>
                      </div>
                    </ScaleIn>
                  )}
                </div>
              </div>
            </SlideIn>

            {/* 右侧：预览 */}
            <SlideIn direction="right" delay={400}>
              <div className="lg:sticky lg:top-8">
                <CertificatePreview
                  template={selectedTemplate}
                  formData={formData}
                />
              </div>
            </SlideIn>
          </div>
        </div>
      </div>


    </div>
  );
}
