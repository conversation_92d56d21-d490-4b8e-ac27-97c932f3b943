'use client';

import React from 'react';
import { CertificateData, CertificateTemplate } from '@/types/certificate';

interface CertificatePreviewProps {
  template: CertificateTemplate;
  formData: CertificateData;
}

export default function CertificatePreview({
  template,
  formData,
}: CertificatePreviewProps) {
  // 根据模板方向确定预览容器的样式
  const previewContainerClass = template.orientation === 'landscape'
    ? 'aspect-[4/3] w-full max-w-2xl mx-auto' // 横向模板预览
    : 'aspect-[3/4] w-full max-w-md mx-auto';  // 竖向模板预览

  return (
    <div className="w-full">
      {/* <h3 className="text-lg font-semibold mb-4 text-gray-900">Preview</h3> */}
      <div className={`${previewContainerClass} relative`}>
        <div
          className="relative h-full w-full rounded-lg shadow-lg overflow-hidden"
          style={{
            backgroundColor: template.style.colors.background,
            fontFamily: template.style.fonts.body.family,
          }}
        >
          {/* 背景图片支持 */}
          {template.backgroundImage && (
            <div className="absolute inset-0">
              <img
                src={template.backgroundImage}
                alt="Certificate background"
                className="w-full h-full object-cover"
              />
            </div>
          )}
          {/* 边框装饰 */}
          {/* <div
            className="absolute inset-2 rounded border-2"
            style={{
              borderColor: template.style.colors.primary,
              borderWidth: template.style.border.includes('3pt') ? '3px' : 
                          template.style.border.includes('2pt') ? '2px' : '1px',
            }}
          /> */}

          {/* 证书内容 - 根据模板方向调整布局 */}
          <div className={`relative h-full flex flex-col ${
            template.orientation === 'landscape' ? 'p-4' : 'p-6'
          }`}>
            {/* 标题 */}
            {/* <div className={`text-center ${
              template.orientation === 'landscape' ? 'mb-4' : 'mb-8'
            }`}>
              <h1
                className="font-bold leading-tight"
                style={{
                  color: template.style.colors.primary,
                  fontFamily: template.style.fonts.title.family,
                  fontSize: `${Math.max(template.style.fonts.title.size * (template.orientation === 'landscape' ? 0.3 : 0.4), 14)}px`,
                  fontWeight: template.style.fonts.title.weight,
                }}
              >
                CERTIFICATE OF ACHIEVEMENT
              </h1>
            </div> */}

            {/* 装饰线 */}
            {/* <div className={`flex justify-center ${
              template.orientation === 'landscape' ? 'mb-3' : 'mb-6'
            }`}>
              <div
                className="h-0.5 w-16"
                style={{ backgroundColor: template.style.colors.secondary }}
              />
            </div> */}

            {/* 收件人姓名 - 只有输入内容时才显示 */}
            {formData.recipientName && (
              <div className={`text-center ${
                template.orientation === 'landscape' ? 'mb-3' : 'mb-6'
              }`}>
                {/* <div className="text-xs text-gray-500 mb-1">This certificate is presented to</div> */}
                <div
                  className="font-semibold leading-tight"
                  style={{
                    color: template.style.fonts.name.color,
                    fontFamily: template.style.fonts.name.family,
                    fontSize: `${Math.max(template.style.fonts.name.size * (template.orientation === 'landscape' ? 0.4 : 0.5), 12)}px`,
                    fontWeight: template.style.fonts.name.weight,
                  }}
                >
                  {formData.recipientName}
                </div>
              </div>
            )}

            {/* 详细信息 - 只有输入内容时才显示 */}
            {formData.details && (
              <div className={`text-center flex-1 flex items-center justify-center ${
                template.orientation === 'landscape' ? 'mb-4' : 'mb-8'
              }`}>
                <div
                  className="leading-relaxed px-4"
                  style={{
                    color: template.style.fonts.body.color,
                    fontFamily: template.style.fonts.body.family,
                    fontSize: `${Math.max(template.style.fonts.body.size * (template.orientation === 'landscape' ? 0.5 : 0.6), 9)}px`,
                    fontWeight: template.style.fonts.body.weight,
                  }}
                >
                  {formData.details}
                </div>
              </div>
            )}

            {/* 日期和签名 - 只有输入内容时才显示 */}
            <div className="flex justify-between items-end">
              {/* 日期 */}
              {formData.date && (
                <div className="text-left">
                  {/* <div className="text-xs text-gray-500 mb-1">Date</div> */}
                  <div
                    className="pb-1"
                    style={{
                      color: template.style.fonts.body.color,
                      fontFamily: template.style.fonts.body.family,
                      fontSize: `${Math.max(template.style.fonts.body.size * (template.orientation === 'landscape' ? 0.5 : 0.6), 9)}px`,
                    }}
                  >
                    {formData.date}
                  </div>
                </div>
              )}

              {/* 签名 */}
              {formData.signature && (
                <div className="text-right">
                  {/* <div className="text-xs text-gray-500 mb-1">Signature</div> */}
                  <div
                    className="pb-1 text-right"
                    style={{
                      color: template.style.fonts.signature.color,
                      fontFamily: template.style.fonts.signature.family,
                      fontSize: `${Math.max(template.style.fonts.signature.size * (template.orientation === 'landscape' ? 0.4 : 0.5), 10)}px`,
                      fontWeight: template.style.fonts.signature.weight,
                    }}
                  >
                    {formData.signature}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 装饰元素 */}
          {/* {template.id === 'classic-business' && (
            <>
              <div
                className="absolute top-4 left-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute top-4 right-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute bottom-4 left-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute bottom-4 right-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
            </>
          )} */}

          {template.id === 'elegant-green' && (
            <>
              <div
                className="absolute top-6 left-1/2 transform -translate-x-1/2 w-12 h-1 opacity-20"
                style={{ backgroundColor: template.style.colors.secondary }}
              />
              <div
                className="absolute bottom-6 left-1/2 transform -translate-x-1/2 w-12 h-1 opacity-20"
                style={{ backgroundColor: template.style.colors.secondary }}
              />
            </>
          )}
        </div>
      </div>

      {/* 预览说明和操作 */}

    </div>
  );
}
