'use client';

import { PDFDocument, PDFPage, PDFFont, StandardFonts, rgb, degrees } from 'pdf-lib';
import { CertificateTemplate, CertificateData } from '@/types/certificate';
import { downloadFile } from '@/lib/utils';
import { measurePerformance } from '@/components/common/PerformanceMonitor';

/**
 * PDF生成器类
 * 负责将证书模板和数据转换为高质量的PDF文件
 */
export class PDFGenerator {
  private template: CertificateTemplate;
  private data: CertificateData;
  private pdfDoc: PDFDocument | null = null;
  private page: PDFPage | null = null;
  private fonts: { [key: string]: PDFFont } = {};

  constructor(template: CertificateTemplate, data: CertificateData) {
    this.template = template;
    this.data = data;
  }

  /**
   * 生成PDF并返回字节数组
   */
  async generate(): Promise<Uint8Array> {
    const startTime = performance.now();
    measurePerformance.mark('pdf-generation-start');

    try {
      // 创建PDF文档
      this.pdfDoc = await PDFDocument.create();
      
      // 设置文档元数据
      this.setDocumentMetadata();
      
      // 根据模板方向添加页面
      const pageSize = this.getPageSize();
      this.page = this.pdfDoc.addPage(pageSize);
      
      // 加载字体
      await this.loadFonts();
      
      // 绘制证书内容
      await this.drawCertificate();
      
      // 返回PDF字节数组
      const pdfBytes = await this.pdfDoc.save();

      // 性能监控
      measurePerformance.mark('pdf-generation-end');
      const endTime = performance.now();
      measurePerformance.measurePDFGeneration(startTime, endTime);
      measurePerformance.measure('pdf-generation-total', 'pdf-generation-start', 'pdf-generation-end');

      return pdfBytes;
    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 生成PDF并自动下载
   */
  async generateAndDownload(): Promise<void> {
    const pdfBytes = await this.generate();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const filename = `certificate-${this.data.recipientName.replace(/[^a-zA-Z0-9]/g, '_')}-${Date.now()}.pdf`;
    downloadFile(blob, filename);
  }

  /**
   * 获取证书标题
   */
  private getCertificateTitle(): string {
    const categoryTitles = {
      'achievement': 'CERTIFICATE OF ACHIEVEMENT',
      'completion': 'CERTIFICATE OF COMPLETION',
      'participation': 'CERTIFICATE OF PARTICIPATION',
      'excellence': 'CERTIFICATE OF EXCELLENCE',
      'custom': 'CERTIFICATE'
    };

    return categoryTitles[this.template.category] || 'CERTIFICATE';
  }

  /**
   * 设置PDF文档元数据
   */
  private setDocumentMetadata(): void {
    if (!this.pdfDoc) return;

    const certificateTitle = this.getCertificateTitle();
    this.pdfDoc.setTitle(`${certificateTitle} - ${this.data.recipientName}`);
    this.pdfDoc.setSubject(certificateTitle);
    this.pdfDoc.setAuthor('Certificate Maker');
    this.pdfDoc.setCreator('Certificate Maker - https://certificate-maker.com');
    this.pdfDoc.setProducer('PDF-lib');
    this.pdfDoc.setCreationDate(new Date());
    this.pdfDoc.setModificationDate(new Date());
  }

  /**
   * 根据模板方向获取页面尺寸
   */
  private getPageSize(): [number, number] {
    // A4尺寸: 595.28 x 841.89 points
    const A4_WIDTH = 595.28;
    const A4_HEIGHT = 841.89;

    if (this.template.orientation === 'landscape') {
      // 横向：宽度 > 高度
      return [A4_HEIGHT, A4_WIDTH]; // 841.89 x 595.28
    } else {
      // 竖向：高度 > 宽度
      return [A4_WIDTH, A4_HEIGHT]; // 595.28 x 841.89
    }
  }

  /**
   * 加载所需字体
   */
  private async loadFonts(): Promise<void> {
    if (!this.pdfDoc) return;

    try {
      // 加载标准字体
      this.fonts.helvetica = this.pdfDoc.embedStandardFont(StandardFonts.Helvetica);
      this.fonts.helveticaBold = this.pdfDoc.embedStandardFont(StandardFonts.HelveticaBold);
      this.fonts.timesRoman = this.pdfDoc.embedStandardFont(StandardFonts.TimesRoman);
      this.fonts.timesRomanBold = this.pdfDoc.embedStandardFont(StandardFonts.TimesRomanBold);
      this.fonts.courier = this.pdfDoc.embedStandardFont(StandardFonts.Courier);
      this.fonts.courierBold = this.pdfDoc.embedStandardFont(StandardFonts.CourierBold);
    } catch (error) {
      console.error('Font loading error:', error);
      throw new Error('Failed to load fonts');
    }
  }

  /**
   * 绘制证书内容
   */
  private async drawCertificate(): Promise<void> {
    if (!this.page) return;

    // 绘制背景和边框
    await this.drawBackground();

    // 绘制装饰元素
    this.drawDecorations();

    // 绘制文本内容
    this.drawTexts();
  }

  /**
   * 绘制背景和边框
   */
  private async drawBackground(): Promise<void> {
    if (!this.page || !this.pdfDoc) return;

    const { width, height } = this.page.getSize();
    const colors = this.template.style.colors;

    // 如果有背景图片，绘制背景图片
    if (this.template.backgroundImage) {
      try {
        await this.drawBackgroundImage();
      } catch (error) {
        console.warn('Failed to load background image, falling back to color background:', error);
        // 如果背景图片加载失败，使用颜色背景
        this.drawColorBackground();
      }
    } else {
      // 绘制颜色背景
      this.drawColorBackground();
    }

    // 对于有背景图片的模板，不绘制边框（因为边框已经在图片中）
    if (!this.template.backgroundImage) {
      // 绘制外边框
      const borderWidth = this.getBorderWidth();
      this.page.drawRectangle({
        x: 20,
        y: 20,
        width: width - 40,
        height: height - 40,
        borderColor: this.hexToRgb(colors.primary),
        borderWidth: borderWidth,
      });

      // 绘制内边框
      this.page.drawRectangle({
        x: 30,
        y: 30,
        width: width - 60,
        height: height - 60,
        borderColor: this.hexToRgb(colors.primary),
        borderWidth: 1,
        opacity: 0.3,
      });
    }
  }

  /**
   * 绘制颜色背景
   */
  private drawColorBackground(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const colors = this.template.style.colors;

    this.page.drawRectangle({
      x: 0,
      y: 0,
      width,
      height,
      color: this.hexToRgb(colors.background),
    });
  }

  /**
   * 绘制背景图片
   */
  private async drawBackgroundImage(): Promise<void> {
    if (!this.page || !this.pdfDoc || !this.template.backgroundImage) return;

    const { width, height } = this.page.getSize();

    try {
      // 获取图片数据
      let imageUrl = this.template.backgroundImage;

      // 如果是相对路径，转换为完整URL
      if (imageUrl.startsWith('/')) {
        imageUrl = `${window.location.origin}${imageUrl}`;
      }

      console.log('Loading background image:', imageUrl);
      const response = await fetch(imageUrl);

      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }

      const imageBytes = await response.arrayBuffer();

      // 根据文件扩展名确定图片类型
      let image;
      if (imageUrl.toLowerCase().endsWith('.png')) {
        image = await this.pdfDoc.embedPng(imageBytes);
      } else if (imageUrl.toLowerCase().endsWith('.jpg') || imageUrl.toLowerCase().endsWith('.jpeg')) {
        image = await this.pdfDoc.embedJpg(imageBytes);
      } else {
        throw new Error(`Unsupported image format: ${imageUrl}`);
      }

      // 获取图片的原始尺寸
      const imageDims = image.scale(1);

      // 计算缩放比例以适应页面
      const scaleX = width / imageDims.width;
      const scaleY = height / imageDims.height;
      const scale = Math.min(scaleX, scaleY);

      // 计算居中位置
      const scaledWidth = imageDims.width * scale;
      const scaledHeight = imageDims.height * scale;
      const x = (width - scaledWidth) / 2;
      const y = (height - scaledHeight) / 2;

      // 绘制图片，保持宽高比并居中
      this.page.drawImage(image, {
        x,
        y,
        width: scaledWidth,
        height: scaledHeight,
      });

      console.log('Background image drawn successfully');
    } catch (error) {
      console.error('Error drawing background image:', error);
      throw error;
    }
  }

  /**
   * 绘制装饰元素
   */
  private drawDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const colors = this.template.style.colors;

    // 根据模板类型绘制不同的装饰
    switch (this.template.id) {
      case 'classic-business':
        this.drawClassicBusinessDecorations();
        break;
      case 'elegant-green':
        this.drawElegantGreenDecorations();
        break;
      case 'modern-purple':
        this.drawModernPurpleDecorations();
        break;
      case 'luxury-gold':
        this.drawLuxuryGoldDecorations();
        break;
    }

    // 绘制装饰线
    const lineY = height - 200;
    this.page.drawLine({
      start: { x: width / 2 - 50, y: lineY },
      end: { x: width / 2 + 50, y: lineY },
      thickness: 2,
      color: this.hexToRgb(colors.secondary),
    });
  }

  /**
   * 绘制文本内容
   */
  private drawTexts(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();

    // 绘制标题
    this.drawTitle();
    
    // 绘制收件人姓名
    this.drawRecipientName();
    
    // 绘制详细信息
    this.drawDetails();
    
    // 绘制日期和签名
    this.drawDateAndSignature();
  }

  /**
   * 绘制标题
   */
  private drawTitle(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const titleLayout = this.template.layout.title;
    const titleFont = this.getFont(titleLayout.fontFamily, titleLayout.fontWeight || 700);
    const titleSize = titleLayout.fontSize;
    const titleColor = this.hexToRgb(titleLayout.color);

    const title = this.getCertificateTitle();

    // 使用模板定义的位置和对齐方式
    let x = titleLayout.x;
    if (titleLayout.align === 'center') {
      const titleWidth = titleFont.widthOfTextAtSize(title, titleSize);
      x = titleLayout.x - titleWidth / 2;
    } else if (titleLayout.align === 'right') {
      const titleWidth = titleFont.widthOfTextAtSize(title, titleSize);
      x = titleLayout.x - titleWidth;
    }

    this.page.drawText(title, {
      x,
      y: height - titleLayout.y,
      size: titleSize,
      font: titleFont,
      color: titleColor,
    });
  }

  /**
   * 绘制收件人姓名
   */
  private drawRecipientName(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const nameLayout = this.template.layout.name;
    const nameFont = this.getFont(nameLayout.fontFamily, nameLayout.fontWeight || 600);
    const nameSize = nameLayout.fontSize;
    const nameColor = this.hexToRgb(nameLayout.color);

    // 使用模板定义的位置和对齐方式
    let x = nameLayout.x;
    if (nameLayout.align === 'center') {
      const nameWidth = nameFont.widthOfTextAtSize(this.data.recipientName, nameSize);
      x = nameLayout.x - nameWidth / 2;
    } else if (nameLayout.align === 'right') {
      const nameWidth = nameFont.widthOfTextAtSize(this.data.recipientName, nameSize);
      x = nameLayout.x - nameWidth;
    }

    this.page.drawText(this.data.recipientName, {
      x,
      y: height - nameLayout.y,
      size: nameSize,
      font: nameFont,
      color: nameColor,
    });
  }

  /**
   * 绘制详细信息
   */
  private drawDetails(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const detailsLayout = this.template.layout.details;
    const bodyFont = this.getFont(detailsLayout.fontFamily, detailsLayout.fontWeight || 400);
    const bodySize = detailsLayout.fontSize;
    const bodyColor = this.hexToRgb(detailsLayout.color);

    // 分割长文本为多行
    const maxWidth = detailsLayout.width;
    const lines = this.wrapText(this.data.details, bodyFont, bodySize, maxWidth);

    const lineHeight = bodySize * 1.5;
    const startY = height - detailsLayout.y;

    lines.forEach((line, index) => {
      let x = detailsLayout.x;
      if (detailsLayout.align === 'center') {
        const lineWidth = bodyFont.widthOfTextAtSize(line, bodySize);
        x = detailsLayout.x - lineWidth / 2;
      } else if (detailsLayout.align === 'right') {
        const lineWidth = bodyFont.widthOfTextAtSize(line, bodySize);
        x = detailsLayout.x - lineWidth;
      }

      this.page!.drawText(line, {
        x,
        y: startY - (index * lineHeight),
        size: bodySize,
        font: bodyFont,
        color: bodyColor,
      });
    });
  }

  /**
   * 绘制日期和签名
   */
  private drawDateAndSignature(): void {
    if (!this.page) return;

    const { height } = this.page.getSize();
    const dateLayout = this.template.layout.date;
    const signatureLayout = this.template.layout.signature;

    // 绘制日期
    const dateFont = this.getFont(dateLayout.fontFamily, dateLayout.fontWeight || 400);
    const dateSize = dateLayout.fontSize;
    const dateColor = this.hexToRgb(dateLayout.color);

    let dateX = dateLayout.x;
    if (dateLayout.align === 'center') {
      const dateWidth = dateFont.widthOfTextAtSize(this.data.date, dateSize);
      dateX = dateLayout.x - dateWidth / 2;
    } else if (dateLayout.align === 'right') {
      const dateWidth = dateFont.widthOfTextAtSize(this.data.date, dateSize);
      dateX = dateLayout.x - dateWidth;
    }

    this.page.drawText(this.data.date, {
      x: dateX,
      y: height - dateLayout.y,
      size: dateSize,
      font: dateFont,
      color: dateColor,
    });

    // 绘制签名
    const signatureFont = this.getFont(signatureLayout.fontFamily, signatureLayout.fontWeight || 400);
    const signatureSize = signatureLayout.fontSize;
    const signatureColor = this.hexToRgb(signatureLayout.color);

    let signatureX = signatureLayout.x;
    if (signatureLayout.align === 'center') {
      const signatureWidth = signatureFont.widthOfTextAtSize(this.data.signature, signatureSize);
      signatureX = signatureLayout.x - signatureWidth / 2;
    } else if (signatureLayout.align === 'right') {
      const signatureWidth = signatureFont.widthOfTextAtSize(this.data.signature, signatureSize);
      signatureX = signatureLayout.x - signatureWidth;
    }

    this.page.drawText(this.data.signature, {
      x: signatureX,
      y: height - signatureLayout.y,
      size: signatureSize,
      font: signatureFont,
      color: signatureColor,
    });
  }

  /**
   * 获取字体
   */
  private getFont(family: string, weight: string | number): PDFFont {
    // 根据字体族和权重返回对应的字体
    const isBold = weight === 'bold' || weight === 700 || weight === 600;

    if (family.toLowerCase().includes('times')) {
      return isBold ? this.fonts.timesRomanBold : this.fonts.timesRoman;
    } else if (family.toLowerCase().includes('courier')) {
      return isBold ? this.fonts.courierBold : this.fonts.courier;
    } else {
      return isBold ? this.fonts.helveticaBold : this.fonts.helvetica;
    }
  }

  /**
   * 将十六进制颜色转换为RGB
   */
  private hexToRgb(hex: string) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) {
      return rgb(0, 0, 0);
    }
    return rgb(
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    );
  }

  /**
   * 获取边框宽度
   */
  private getBorderWidth(): number {
    const borderStyle = this.template.style.border;
    if (borderStyle.includes('3pt')) return 3;
    if (borderStyle.includes('2pt')) return 2;
    return 1;
  }

  /**
   * 文本换行处理
   */
  private wrapText(text: string, font: PDFFont, fontSize: number, maxWidth: number): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = font.widthOfTextAtSize(testLine, fontSize);

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
          currentLine = word;
        } else {
          // 单词太长，强制换行
          lines.push(word);
        }
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }

  /**
   * 绘制经典商务风格装饰
   */
  private drawClassicBusinessDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const primaryColor = this.hexToRgb(this.template.style.colors.primary);

    // 绘制四角装饰圆圈
    const circleRadius = 15;
    const margin = 50;

    // 左上角
    this.page.drawCircle({
      x: margin,
      y: height - margin,
      size: circleRadius,
      color: primaryColor,
      opacity: 0.1,
    });

    // 右上角
    this.page.drawCircle({
      x: width - margin,
      y: height - margin,
      size: circleRadius,
      color: primaryColor,
      opacity: 0.1,
    });

    // 左下角
    this.page.drawCircle({
      x: margin,
      y: margin,
      size: circleRadius,
      color: primaryColor,
      opacity: 0.1,
    });

    // 右下角
    this.page.drawCircle({
      x: width - margin,
      y: margin,
      size: circleRadius,
      color: primaryColor,
      opacity: 0.1,
    });
  }

  /**
   * 绘制优雅绿色风格装饰
   */
  private drawElegantGreenDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const secondaryColor = this.hexToRgb(this.template.style.colors.secondary);

    // 绘制顶部装饰线
    this.page.drawRectangle({
      x: width / 2 - 60,
      y: height - 80,
      width: 120,
      height: 3,
      color: secondaryColor,
      opacity: 0.2,
    });

    // 绘制底部装饰线
    this.page.drawRectangle({
      x: width / 2 - 60,
      y: 80,
      width: 120,
      height: 3,
      color: secondaryColor,
      opacity: 0.2,
    });
  }

  /**
   * 绘制现代紫色风格装饰
   */
  private drawModernPurpleDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const primaryColor = this.hexToRgb(this.template.style.colors.primary);

    // 绘制几何装饰 - 使用矩形代替三角形
    const rectSize = 15;
    const centerX = width / 2;
    const topY = height - 100;

    // 绘制菱形装饰
    this.page.drawRectangle({
      x: centerX - rectSize / 2,
      y: topY - rectSize / 2,
      width: rectSize,
      height: rectSize,
      color: primaryColor,
      opacity: 0.15,
      rotate: degrees(45),
    });
  }

  /**
   * 绘制奢华金色风格装饰
   */
  private drawLuxuryGoldDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const primaryColor = this.hexToRgb(this.template.style.colors.primary);

    // 绘制装饰边框
    const decorMargin = 40;
    this.page.drawRectangle({
      x: decorMargin,
      y: decorMargin,
      width: width - decorMargin * 2,
      height: height - decorMargin * 2,
      borderColor: primaryColor,
      borderWidth: 2,
      opacity: 0.3,
    });

    // 绘制内部装饰线
    const innerMargin = 50;
    this.page.drawLine({
      start: { x: innerMargin, y: height - innerMargin },
      end: { x: width - innerMargin, y: height - innerMargin },
      thickness: 1,
      color: primaryColor,
      opacity: 0.2,
    });

    this.page.drawLine({
      start: { x: innerMargin, y: innerMargin },
      end: { x: width - innerMargin, y: innerMargin },
      thickness: 1,
      color: primaryColor,
      opacity: 0.2,
    });
  }
}

/**
 * 便捷函数：生成PDF并下载
 */
export async function generateCertificatePDF(
  template: CertificateTemplate,
  data: CertificateData
): Promise<void> {
  const generator = new PDFGenerator(template, data);
  await generator.generateAndDownload();
}

/**
 * 便捷函数：生成PDF字节数组
 */
export async function generateCertificatePDFBytes(
  template: CertificateTemplate,
  data: CertificateData
): Promise<Uint8Array> {
  const generator = new PDFGenerator(template, data);
  return await generator.generate();
}
